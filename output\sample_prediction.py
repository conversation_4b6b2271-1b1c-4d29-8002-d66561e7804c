
def predict_difficult_laryngoscopy(ttohr, shbd, sthmd, sed, model_path='output/models/best_model.pkl'):
    """
    Predict difficult laryngoscopy for a new patient.

    Args:
        ttohr: TTOHR measurement
        shbd: SHBD measurement
        sthmd: STHMD measurement
        sed: SED measurement
        model_path: Path to trained model

    Returns:
        Prediction (0=Easy, 1=Difficult) and probability
    """
    import joblib
    import numpy as np

    # Load trained model and scaler
    model = joblib.load(model_path)
    scaler = joblib.load('output/models/scaler.pkl')

    # Prepare input data
    input_data = np.array([[ttohr, shbd, sthmd, sed]])
    input_scaled = scaler.transform(input_data)

    # Make prediction
    prediction = model.predict(input_scaled)[0]
    probability = model.predict_proba(input_scaled)[0, 1] if hasattr(model, 'predict_proba') else None

    return prediction, probability

# Example usage:
# prediction, prob = predict_difficult_laryngoscopy(0.8, 1.5, 1.8, 2.0)
# print(f"Prediction: {'Difficult' if prediction else 'Easy'} (Probability: {prob:.3f})")
