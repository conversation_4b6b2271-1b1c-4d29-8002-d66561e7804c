library(readxl)
sam <- read_excel("SAMBIT THESIS DATA version2).xlsx")

library(pROC)
names(sam)

ttohr_roc = roc(sam$`Difficult Laryngoscopy`,sam$TTOHR)
plot(ttohr_roc)
auc(ttohr_roc)

coords(ttohr_roc,"best")
coords(ttohr_roc,"best",ret = c("se","sp","ppv",
                                "npv","accuracy"))

ci.auc(ttohr_roc)
ci.coords(ttohr_roc,x=0.79,ret = c("se"))
ci.coords(ttohr_roc,x=0.79,ret = c("sp"))
ci.coords(ttohr_roc,x=0.79,ret = c("accuracy"))

names(sam)
fit1=glm(`Difficult Laryngoscopy`~TTOHR+SHBD, 
         family='binomial',data = sam)
fit2=glm(`Difficult Laryngoscopy`~TTOHR, 
         family='binomial',data = sam)
fit3=glm(`Difficult Laryngoscopy`~TTOHR+SHBD+SED, 
         family='binomial',data = sam)
summary(fit3)

preds=predict(fit1)
roc1=roc(sam$`Difficult Laryngoscopy` ~ preds)

preds2=predict(fit2)
roc2=roc(sam$`Difficult Laryngoscopy` ~ preds2)

preds3=predict(fit3)
roc3=roc(sam$`Difficult Laryngoscopy` ~ preds3)

pre

plot(roc1)
plot(roc2, add=TRUE, col='red')
plot(roc3, add=TRUE, col='green')

roc.test(roc3,roc2)
