#!/usr/bin/env python3
"""
Basic Machine Learning Models for Difficult Laryngoscopy Prediction

This script implements multiple classification models to predict "Difficult Laryngoscopy"
using medical dataset features. It replicates and extends the R analysis with Python ML techniques.

Author: AI Assistant
Date: 2024
"""

import os
import sys
import warnings
import logging
import argparse
import pickle
from datetime import datetime
from typing import Dict, List, Tuple, Any, Optional

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.model_selection import train_test_split, cross_val_score, StratifiedKFold
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.svm import SVC
from sklearn.tree import DecisionTreeClassifier
from sklearn.neighbors import KNeighborsClassifier
from sklearn.naive_bayes import GaussianNB
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    confusion_matrix, classification_report, roc_curve, auc,
    roc_auc_score
)
from sklearn.pipeline import Pipeline
import joblib

# Configure warnings and logging
warnings.filterwarnings('ignore')
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('basic_models.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Set random seed for reproducibility
RANDOM_STATE = 42
np.random.seed(RANDOM_STATE)

class DifficultyLaryngoscopyPredictor:
    """
    A comprehensive machine learning pipeline for predicting difficult laryngoscopy.

    This class handles data loading, preprocessing, model training, evaluation,
    and comparison with R analysis results.
    """

    def __init__(self, data_path: str = "SAMBIT THESIS DATA version3.xlsx"):
        """
        Initialize the predictor with data path.

        Args:
            data_path: Path to the Excel file containing the dataset
        """
        self.data_path = data_path
        self.data = None
        self.X = None
        self.y = None
        self.X_train = None
        self.X_test = None
        self.y_train = None
        self.y_test = None
        self.scaler = None
        self.models = {}
        self.results = {}
        self.feature_names = ['TTOHR', 'SHBD', 'STHMD', 'SED']

        # Create output directories
        self._create_output_dirs()

    def _create_output_dirs(self) -> None:
        """Create necessary output directories."""
        dirs = ['output', 'output/figures', 'output/models', 'output/results']
        for dir_path in dirs:
            os.makedirs(dir_path, exist_ok=True)
        logger.info("Created output directories")

    def load_and_validate_data(self) -> pd.DataFrame:
        """
        Load and validate the dataset from Excel file.

        Returns:
            Loaded and validated DataFrame

        Raises:
            FileNotFoundError: If the data file is not found
            ValueError: If required columns are missing
        """
        try:
            # Try loading from Excel first
            if os.path.exists(self.data_path):
                self.data = pd.read_excel(self.data_path)
                logger.info(f"Successfully loaded data from {self.data_path}")
            else:
                # Fallback to CSV if Excel not found
                csv_path = "ai.csv"
                if os.path.exists(csv_path):
                    self.data = pd.read_csv(csv_path)
                    logger.info(f"Loaded data from fallback CSV: {csv_path}")
                else:
                    raise FileNotFoundError(f"Neither {self.data_path} nor {csv_path} found")

            # Validate required columns
            required_cols = self.feature_names + ['Difficult Laryngoscopy']

            # Handle different possible column names for target variable
            target_col = None
            possible_target_names = [
                'Difficult Laryngoscopy',
                'Difficult Laryngoscopy...24',
                'INTUBATION'
            ]

            for col_name in possible_target_names:
                if col_name in self.data.columns:
                    target_col = col_name
                    break

            if target_col is None:
                raise ValueError(f"Target column not found. Available columns: {list(self.data.columns)}")

            # Standardize target column name
            if target_col != 'Difficult Laryngoscopy':
                self.data['Difficult Laryngoscopy'] = self.data[target_col]

            # Check for missing feature columns
            missing_cols = [col for col in self.feature_names if col not in self.data.columns]
            if missing_cols:
                raise ValueError(f"Missing required columns: {missing_cols}")

            logger.info(f"Dataset shape: {self.data.shape}")
            logger.info(f"Columns: {list(self.data.columns)}")
            logger.info("Data validation successful")

            return self.data

        except Exception as e:
            logger.error(f"Error loading data: {str(e)}")
            raise

    def exploratory_data_analysis(self) -> None:
        """
        Perform comprehensive exploratory data analysis.
        """
        logger.info("Starting exploratory data analysis...")

        # Basic statistics
        print("\n" + "="*50)
        print("DATASET OVERVIEW")
        print("="*50)
        print(f"Dataset shape: {self.data.shape}")
        print(f"Features: {self.feature_names}")
        print("\nFirst 5 rows:")
        print(self.data[self.feature_names + ['Difficult Laryngoscopy']].head())

        print("\nSummary Statistics:")
        summary_stats = self.data[self.feature_names].describe()
        print(summary_stats)

        # Target variable distribution
        target_counts = self.data['Difficult Laryngoscopy'].value_counts()
        print(f"\nTarget Variable Distribution:")
        print(f"Easy Laryngoscopy (0): {target_counts.get(0, 0)}")
        print(f"Difficult Laryngoscopy (1): {target_counts.get(1, 0)}")

        # Save summary statistics
        summary_stats.to_csv('output/results/summary_statistics.csv')

        # Create visualizations
        self._create_eda_plots()

        logger.info("Exploratory data analysis completed")

    def _create_eda_plots(self) -> None:
        """Create comprehensive EDA visualizations."""
        # Set style
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")

        # 1. Distribution plots
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Feature Distributions', fontsize=16, fontweight='bold')

        for i, feature in enumerate(self.feature_names):
            row, col = i // 2, i % 2
            axes[row, col].hist(self.data[feature], bins=20, alpha=0.7, edgecolor='black')
            axes[row, col].set_title(f'{feature} Distribution', fontweight='bold')
            axes[row, col].set_xlabel(feature)
            axes[row, col].set_ylabel('Frequency')
            axes[row, col].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('output/figures/feature_distributions.png', dpi=300, bbox_inches='tight')
        plt.close()

        # 2. Box plots for outlier detection
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Box Plots - Outlier Detection', fontsize=16, fontweight='bold')

        for i, feature in enumerate(self.feature_names):
            row, col = i // 2, i % 2
            axes[row, col].boxplot(self.data[feature])
            axes[row, col].set_title(f'{feature} Box Plot', fontweight='bold')
            axes[row, col].set_ylabel(feature)
            axes[row, col].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('output/figures/boxplots_outliers.png', dpi=300, bbox_inches='tight')
        plt.close()

        # 3. Correlation heatmap
        plt.figure(figsize=(10, 8))
        correlation_matrix = self.data[self.feature_names].corr()
        mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))

        sns.heatmap(correlation_matrix,
                   mask=mask,
                   annot=True,
                   cmap='coolwarm',
                   center=0,
                   square=True,
                   fmt='.3f',
                   cbar_kws={"shrink": .8})
        plt.title('Feature Correlation Matrix', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig('output/figures/correlation_heatmap.png', dpi=300, bbox_inches='tight')
        plt.close()

        # 4. Target variable distribution
        plt.figure(figsize=(10, 6))
        target_counts = self.data['Difficult Laryngoscopy'].value_counts()

        plt.subplot(1, 2, 1)
        bars = plt.bar(['Easy (0)', 'Difficult (1)'], target_counts.values,
                      color=['lightblue', 'lightcoral'], edgecolor='black')
        plt.title('Target Variable Distribution', fontweight='bold')
        plt.ylabel('Count')

        # Add value labels on bars
        for bar, count in zip(bars, target_counts.values):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                    str(count), ha='center', va='bottom', fontweight='bold')

        plt.subplot(1, 2, 2)
        plt.pie(target_counts.values, labels=['Easy (0)', 'Difficult (1)'],
               autopct='%1.1f%%', colors=['lightblue', 'lightcoral'])
        plt.title('Target Variable Proportion', fontweight='bold')

        plt.tight_layout()
        plt.savefig('output/figures/target_distribution.png', dpi=300, bbox_inches='tight')
        plt.close()

        logger.info("EDA plots saved to output/figures/")

    def preprocess_data(self) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """
        Preprocess the data following R analysis methodology.

        Returns:
            Tuple of (X_train, X_test, y_train, y_test)
        """
        logger.info("Starting data preprocessing...")

        # Extract features and target (excluding HEIGHT as specified)
        X = self.data[self.feature_names].copy()

        # Handle target variable - convert to binary if needed
        y = self.data['Difficult Laryngoscopy'].copy()

        # Convert target to binary (0/1) if it's not already
        if y.dtype == 'object':
            y = (y == 'DIFFICULT').astype(int)
        elif y.max() > 1:
            # If values are like 1,2,3,4 convert to binary
            y = (y > 2).astype(int)  # Assuming >2 means difficult

        print(f"\nTarget variable distribution after conversion:")
        print(f"Easy (0): {(y == 0).sum()}")
        print(f"Difficult (1): {(y == 1).sum()}")

        # Handle missing values
        missing_counts = X.isnull().sum()
        if missing_counts.any():
            logger.warning(f"Missing values found: {missing_counts[missing_counts > 0]}")
            # Use median imputation for numerical features
            X = X.fillna(X.median())
            logger.info("Missing values imputed using median")

        # Apply min-max normalization (matching R implementation)
        # R formula: (X-min(X))/(max(X)-min(X))
        self.scaler = MinMaxScaler()
        X_scaled = self.scaler.fit_transform(X)
        X_scaled = pd.DataFrame(X_scaled, columns=self.feature_names)

        logger.info("Applied min-max scaling to match R normalization")

        # Split data (80% train, 20% test with stratification)
        self.X_train, self.X_test, self.y_train, self.y_test = train_test_split(
            X_scaled, y,
            test_size=0.2,
            random_state=RANDOM_STATE,
            stratify=y
        )

        # Store original features for reference
        self.X = X_scaled
        self.y = y

        print(f"\nData split:")
        print(f"Training set: {self.X_train.shape[0]} samples")
        print(f"Test set: {self.X_test.shape[0]} samples")
        print(f"Training set class distribution: {np.bincount(self.y_train)}")
        print(f"Test set class distribution: {np.bincount(self.y_test)}")

        # Save preprocessed data
        self._save_preprocessed_data()

        logger.info("Data preprocessing completed")
        return self.X_train, self.X_test, self.y_train, self.y_test

    def _save_preprocessed_data(self) -> None:
        """Save preprocessed datasets for reproducibility."""
        # Save scaler
        joblib.dump(self.scaler, 'output/models/scaler.pkl')

        # Save datasets
        datasets = {
            'X_train': self.X_train,
            'X_test': self.X_test,
            'y_train': self.y_train,
            'y_test': self.y_test,
            'feature_names': self.feature_names
        }

        with open('output/results/preprocessed_data.pkl', 'wb') as f:
            pickle.dump(datasets, f)

        # Save as CSV for easy inspection
        train_data = self.X_train.copy()
        train_data['target'] = self.y_train
        train_data.to_csv('output/results/train_data.csv', index=False)

        test_data = self.X_test.copy()
        test_data['target'] = self.y_test
        test_data.to_csv('output/results/test_data.csv', index=False)

        logger.info("Preprocessed data saved")

    def initialize_models(self) -> Dict[str, Any]:
        """
        Initialize all classification models with various configurations.

        Returns:
            Dictionary of initialized models
        """
        logger.info("Initializing classification models...")

        self.models = {
            # Logistic Regression (matching R GLM)
            'Logistic_Regression_L1': LogisticRegression(
                penalty='l1', solver='liblinear', random_state=RANDOM_STATE, max_iter=1000
            ),
            'Logistic_Regression_L2': LogisticRegression(
                penalty='l2', solver='lbfgs', random_state=RANDOM_STATE, max_iter=1000
            ),
            'Logistic_Regression_Elastic': LogisticRegression(
                penalty='elasticnet', solver='saga', l1_ratio=0.5,
                random_state=RANDOM_STATE, max_iter=1000
            ),

            # Random Forest with different configurations
            'Random_Forest_50': RandomForestClassifier(
                n_estimators=50, random_state=RANDOM_STATE, max_depth=5
            ),
            'Random_Forest_100': RandomForestClassifier(
                n_estimators=100, random_state=RANDOM_STATE, max_depth=10
            ),
            'Random_Forest_200': RandomForestClassifier(
                n_estimators=200, random_state=RANDOM_STATE, max_depth=None
            ),

            # Support Vector Machine
            'SVM_Linear': SVC(
                kernel='linear', probability=True, random_state=RANDOM_STATE
            ),
            'SVM_RBF': SVC(
                kernel='rbf', probability=True, random_state=RANDOM_STATE
            ),
            'SVM_Poly': SVC(
                kernel='poly', degree=3, probability=True, random_state=RANDOM_STATE
            ),

            # Decision Tree
            'Decision_Tree': DecisionTreeClassifier(
                random_state=RANDOM_STATE, max_depth=10, min_samples_split=5
            ),

            # Gradient Boosting
            'Gradient_Boosting': GradientBoostingClassifier(
                n_estimators=100, learning_rate=0.1, random_state=RANDOM_STATE
            ),

            # K-Nearest Neighbors
            'KNN_3': KNeighborsClassifier(n_neighbors=3),
            'KNN_5': KNeighborsClassifier(n_neighbors=5),
            'KNN_7': KNeighborsClassifier(n_neighbors=7),

            # Naive Bayes
            'Naive_Bayes': GaussianNB()
        }

        logger.info(f"Initialized {len(self.models)} models")
        return self.models

    def calculate_specificity(self, y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """
        Calculate specificity (True Negative Rate).

        Args:
            y_true: True labels
            y_pred: Predicted labels

        Returns:
            Specificity score
        """
        tn, fp, _, _ = confusion_matrix(y_true, y_pred).ravel()
        return tn / (tn + fp) if (tn + fp) > 0 else 0.0

    def train_and_evaluate_models(self) -> Dict[str, Dict[str, float]]:
        """
        Train and evaluate all models with comprehensive metrics.

        Returns:
            Dictionary containing evaluation results for all models
        """
        logger.info("Starting model training and evaluation...")

        results = {}

        for model_name, model in self.models.items():
            logger.info(f"Training {model_name}...")

            try:
                # Train the model
                model.fit(self.X_train, self.y_train)

                # Make predictions
                y_pred = model.predict(self.X_test)
                y_pred_proba = model.predict_proba(self.X_test)[:, 1] if hasattr(model, 'predict_proba') else None

                # Calculate metrics
                metrics = {
                    'accuracy': accuracy_score(self.y_test, y_pred),
                    'precision': precision_score(self.y_test, y_pred, zero_division=0),
                    'recall': recall_score(self.y_test, y_pred, zero_division=0),
                    'f1_score': f1_score(self.y_test, y_pred, zero_division=0),
                    'specificity': self.calculate_specificity(self.y_test, y_pred)
                }

                # Add AUC if probability predictions available
                if y_pred_proba is not None:
                    metrics['auc'] = roc_auc_score(self.y_test, y_pred_proba)
                else:
                    metrics['auc'] = np.nan

                # Cross-validation scores
                cv_scores = cross_val_score(model, self.X_train, self.y_train,
                                          cv=StratifiedKFold(n_splits=5, shuffle=True, random_state=RANDOM_STATE),
                                          scoring='accuracy')
                metrics['cv_mean'] = cv_scores.mean()
                metrics['cv_std'] = cv_scores.std()

                results[model_name] = metrics

                # Save trained model
                model_path = f'output/models/{model_name}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.pkl'
                joblib.dump(model, model_path)

                logger.info(f"{model_name} - Accuracy: {metrics['accuracy']:.3f}, AUC: {metrics['auc']:.3f}")

            except Exception as e:
                logger.error(f"Error training {model_name}: {str(e)}")
                results[model_name] = {'error': str(e)}

        self.results = results

        # Save results
        results_df = pd.DataFrame(results).T
        results_df.to_csv('output/results/model_evaluation_results.csv')

        logger.info("Model training and evaluation completed")
        return results

    def generate_roc_curves(self) -> None:
        """Generate ROC curves for all models with probability predictions."""
        logger.info("Generating ROC curves...")

        plt.figure(figsize=(12, 8))

        for model_name, model in self.models.items():
            if hasattr(model, 'predict_proba'):
                try:
                    y_pred_proba = model.predict_proba(self.X_test)[:, 1]
                    fpr, tpr, _ = roc_curve(self.y_test, y_pred_proba)
                    auc_score = auc(fpr, tpr)

                    plt.plot(fpr, tpr, label=f'{model_name} (AUC = {auc_score:.3f})')

                except Exception as e:
                    logger.warning(f"Could not generate ROC curve for {model_name}: {str(e)}")

        plt.plot([0, 1], [0, 1], 'k--', label='Random Classifier')
        plt.xlim([0.0, 1.0])
        plt.ylim([0.0, 1.05])
        plt.xlabel('False Positive Rate')
        plt.ylabel('True Positive Rate')
        plt.title('ROC Curves - Model Comparison')
        plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig('output/figures/roc_curves_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()

        logger.info("ROC curves saved")

    def generate_confusion_matrices(self) -> None:
        """Generate confusion matrices for all models."""
        logger.info("Generating confusion matrices...")

        n_models = len(self.models)
        n_cols = 4
        n_rows = (n_models + n_cols - 1) // n_cols

        fig, axes = plt.subplots(n_rows, n_cols, figsize=(20, 5 * n_rows))
        axes = axes.flatten() if n_rows > 1 else [axes] if n_cols == 1 else axes

        for idx, (model_name, model) in enumerate(self.models.items()):
            try:
                y_pred = model.predict(self.X_test)
                cm = confusion_matrix(self.y_test, y_pred)

                # Normalize confusion matrix
                cm_normalized = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]

                sns.heatmap(cm_normalized, annot=True, fmt='.2f', cmap='Blues',
                           xticklabels=['Easy', 'Difficult'],
                           yticklabels=['Easy', 'Difficult'],
                           ax=axes[idx])
                axes[idx].set_title(f'{model_name}')
                axes[idx].set_xlabel('Predicted')
                axes[idx].set_ylabel('Actual')

            except Exception as e:
                axes[idx].text(0.5, 0.5, f'Error: {str(e)}',
                              ha='center', va='center', transform=axes[idx].transAxes)
                axes[idx].set_title(f'{model_name} (Error)')

        # Hide unused subplots
        for idx in range(len(self.models), len(axes)):
            axes[idx].set_visible(False)

        plt.tight_layout()
        plt.savefig('output/figures/confusion_matrices.png', dpi=300, bbox_inches='tight')
        plt.close()

        logger.info("Confusion matrices saved")

    def compare_with_r_analysis(self) -> None:
        """
        Compare results with R analysis and generate comparison report.
        """
        logger.info("Comparing results with R analysis...")

        # R analysis results (from the R scripts we analyzed)
        r_results = {
            'GLM_TTOHR_SHBD': {'features': ['TTOHR', 'SHBD']},
            'GLM_TTOHR': {'features': ['TTOHR']},
            'GLM_TTOHR_SHBD_SED': {'features': ['TTOHR', 'SHBD', 'SED']},
            'GLM_All_Features': {'features': ['TTOHR', 'SHBD', 'STHMD', 'SED']}
        }

        # Train equivalent models for comparison
        comparison_models = {}

        for r_model_name, r_config in r_results.items():
            features = r_config['features']
            feature_indices = [self.feature_names.index(f) for f in features if f in self.feature_names]

            if feature_indices:
                X_train_subset = self.X_train.iloc[:, feature_indices]
                X_test_subset = self.X_test.iloc[:, feature_indices]

                # Train logistic regression (equivalent to R GLM)
                lr_model = LogisticRegression(random_state=RANDOM_STATE, max_iter=1000)
                lr_model.fit(X_train_subset, self.y_train)

                y_pred = lr_model.predict(X_test_subset)
                y_pred_proba = lr_model.predict_proba(X_test_subset)[:, 1]

                comparison_models[r_model_name] = {
                    'accuracy': accuracy_score(self.y_test, y_pred),
                    'auc': roc_auc_score(self.y_test, y_pred_proba),
                    'features': features,
                    'n_features': len(features)
                }

        # Create comparison report
        comparison_df = pd.DataFrame(comparison_models).T
        comparison_df.to_csv('output/results/r_analysis_comparison.csv')

        print("\n" + "="*60)
        print("COMPARISON WITH R ANALYSIS")
        print("="*60)
        print("Python GLM-equivalent models (Logistic Regression):")
        print(comparison_df)

        logger.info("R analysis comparison completed")

    def generate_summary_report(self) -> None:
        """Generate a comprehensive summary report."""
        logger.info("Generating summary report...")

        # Best models by different metrics
        results_df = pd.DataFrame(self.results).T

        if not results_df.empty:
            best_models = {
                'Best Accuracy': results_df['accuracy'].idxmax(),
                'Best AUC': results_df['auc'].idxmax(),
                'Best F1-Score': results_df['f1_score'].idxmax(),
                'Best Precision': results_df['precision'].idxmax(),
                'Best Recall': results_df['recall'].idxmax()
            }

            print("\n" + "="*60)
            print("MODEL PERFORMANCE SUMMARY")
            print("="*60)
            print("Top 5 models by accuracy:")
            top_accuracy = results_df.nlargest(5, 'accuracy')[['accuracy', 'auc', 'f1_score']]
            print(top_accuracy)

            print(f"\nBest models by metric:")
            for metric, model_name in best_models.items():
                if model_name in results_df.index:
                    score = results_df.loc[model_name, metric.lower().replace('best ', '')]
                    print(f"{metric}: {model_name} ({score:.3f})")

            # Save summary
            with open('output/results/summary_report.txt', 'w') as f:
                f.write("DIFFICULT LARYNGOSCOPY PREDICTION - MODEL SUMMARY\n")
                f.write("="*60 + "\n\n")
                f.write(f"Dataset: {self.data.shape[0]} patients\n")
                f.write(f"Features: {', '.join(self.feature_names)}\n")
                f.write(f"Models trained: {len(self.models)}\n\n")
                f.write("Best models by metric:\n")
                for metric, model_name in best_models.items():
                    if model_name in results_df.index:
                        score = results_df.loc[model_name, metric.lower().replace('best ', '')]
                        f.write(f"{metric}: {model_name} ({score:.3f})\n")

        logger.info("Summary report generated")

    def run_complete_analysis(self) -> None:
        """
        Run the complete machine learning analysis pipeline.
        """
        logger.info("Starting complete analysis pipeline...")

        try:
            # Load and validate data
            self.load_and_validate_data()

            # Exploratory data analysis
            self.exploratory_data_analysis()

            # Preprocess data
            self.preprocess_data()

            # Initialize models
            self.initialize_models()

            # Train and evaluate models
            self.train_and_evaluate_models()

            # Generate visualizations
            self.generate_roc_curves()
            self.generate_confusion_matrices()

            # Compare with R analysis
            self.compare_with_r_analysis()

            # Generate summary report
            self.generate_summary_report()

            logger.info("Complete analysis pipeline finished successfully!")
            print("\n" + "="*60)
            print("ANALYSIS COMPLETED SUCCESSFULLY!")
            print("="*60)
            print("Check the 'output' directory for:")
            print("- Figures: output/figures/")
            print("- Models: output/models/")
            print("- Results: output/results/")
            print("- Logs: basic_models.log")

        except Exception as e:
            logger.error(f"Analysis pipeline failed: {str(e)}")
            raise


def create_sample_prediction_function():
    """
    Create a sample function for making predictions on new patient data.
    """
    sample_code = '''
def predict_difficult_laryngoscopy(ttohr, shbd, sthmd, sed, model_path='output/models/best_model.pkl'):
    """
    Predict difficult laryngoscopy for a new patient.

    Args:
        ttohr: TTOHR measurement
        shbd: SHBD measurement
        sthmd: STHMD measurement
        sed: SED measurement
        model_path: Path to trained model

    Returns:
        Prediction (0=Easy, 1=Difficult) and probability
    """
    import joblib
    import numpy as np

    # Load trained model and scaler
    model = joblib.load(model_path)
    scaler = joblib.load('output/models/scaler.pkl')

    # Prepare input data
    input_data = np.array([[ttohr, shbd, sthmd, sed]])
    input_scaled = scaler.transform(input_data)

    # Make prediction
    prediction = model.predict(input_scaled)[0]
    probability = model.predict_proba(input_scaled)[0, 1] if hasattr(model, 'predict_proba') else None

    return prediction, probability

# Example usage:
# prediction, prob = predict_difficult_laryngoscopy(0.8, 1.5, 1.8, 2.0)
# print(f"Prediction: {'Difficult' if prediction else 'Easy'} (Probability: {prob:.3f})")
'''

    with open('output/sample_prediction.py', 'w') as f:
        f.write(sample_code)

    logger.info("Sample prediction function created")


def main():
    """
    Main function with command-line interface.
    """
    parser = argparse.ArgumentParser(
        description='Basic Machine Learning Models for Difficult Laryngoscopy Prediction'
    )
    parser.add_argument(
        '--data',
        type=str,
        default='SAMBIT THESIS DATA version3.xlsx',
        help='Path to the dataset file'
    )
    parser.add_argument(
        '--quick',
        action='store_true',
        help='Run quick analysis with fewer models'
    )
    parser.add_argument(
        '--no-plots',
        action='store_true',
        help='Skip generating plots'
    )

    args = parser.parse_args()

    # Initialize predictor
    predictor = DifficultyLaryngoscopyPredictor(data_path=args.data)

    if args.quick:
        # Quick analysis with fewer models
        logger.info("Running quick analysis...")
        predictor.load_and_validate_data()
        predictor.preprocess_data()

        # Initialize only key models
        predictor.models = {
            'Logistic_Regression': LogisticRegression(random_state=RANDOM_STATE),
            'Random_Forest': RandomForestClassifier(random_state=RANDOM_STATE),
            'SVM_RBF': SVC(kernel='rbf', probability=True, random_state=RANDOM_STATE)
        }

        predictor.train_and_evaluate_models()
        predictor.generate_summary_report()

    else:
        # Full analysis
        predictor.run_complete_analysis()

    # Create sample prediction function
    create_sample_prediction_function()

    print(f"\nAnalysis completed! Check the logs for details.")


if __name__ == "__main__":
    main()
