2025-05-28 21:24:09,420 - INFO - Created output directories
2025-05-28 21:24:09,420 - INFO - Starting complete analysis pipeline...
2025-05-28 21:24:09,787 - INFO - Successfully loaded data from SAMBIT THESIS DATA version3.xlsx
2025-05-28 21:24:09,787 - INFO - Dataset shape: (120, 28)
2025-05-28 21:24:09,787 - INFO - Columns: ['CASE NO', 'NAME', 'AGE', 'SEX', 'Difficult Laryngoscopy ', 'HEIGHT', 'HEIGHT IN METER', 'WEIGHT', 'BMI', 'ASA GRADE', 'MPG SCORE', 'INTER INCISOR DISTANCE', 'SHBD', 'STHMD', 'SED', 'TH', 'OH', 'TTOHR', 'TTFUSG', "HAN'S SCORE", 'HANS_Category', 'CL GRADE', 'CL GRADE.1', 'Difficult Laryngoscopy .1', 'INTUBATION', 'NO OF ATTEMPTS', 'CHANGE OF HAND', 'Difficult Laryngoscopy']
2025-05-28 21:24:09,787 - INFO - Data validation successful
2025-05-28 21:24:09,787 - INFO - Starting exploratory data analysis...
2025-05-28 21:24:13,086 - INFO - EDA plots saved to output/figures/
2025-05-28 21:24:13,086 - INFO - Exploratory data analysis completed
2025-05-28 21:24:13,086 - INFO - Starting data preprocessing...
2025-05-28 21:24:13,086 - INFO - Applied min-max scaling to match R normalization
2025-05-28 21:24:13,110 - INFO - Preprocessed data saved
2025-05-28 21:24:13,110 - INFO - Data preprocessing completed
2025-05-28 21:24:13,110 - INFO - Initializing classification models...
2025-05-28 21:24:13,110 - INFO - Initialized 15 models
2025-05-28 21:24:13,110 - INFO - Starting model training and evaluation...
2025-05-28 21:24:13,110 - INFO - Training Logistic_Regression_L1...
2025-05-28 21:24:13,150 - INFO - Logistic_Regression_L1 - Accuracy: 0.917, AUC: 0.943
2025-05-28 21:24:13,150 - INFO - Training Logistic_Regression_L2...
2025-05-28 21:24:13,202 - INFO - Logistic_Regression_L2 - Accuracy: 0.917, AUC: 0.955
2025-05-28 21:24:13,202 - INFO - Training Logistic_Regression_Elastic...
2025-05-28 21:24:13,255 - INFO - Logistic_Regression_Elastic - Accuracy: 0.917, AUC: 0.955
2025-05-28 21:24:13,265 - INFO - Training Random_Forest_50...
2025-05-28 21:24:13,687 - INFO - Random_Forest_50 - Accuracy: 0.917, AUC: 0.955
2025-05-28 21:24:13,687 - INFO - Training Random_Forest_100...
2025-05-28 21:24:14,447 - INFO - Random_Forest_100 - Accuracy: 0.958, AUC: 0.955
2025-05-28 21:24:14,447 - INFO - Training Random_Forest_200...
2025-05-28 21:24:15,946 - INFO - Random_Forest_200 - Accuracy: 0.875, AUC: 0.955
2025-05-28 21:24:15,946 - INFO - Training SVM_Linear...
2025-05-28 21:24:15,989 - INFO - SVM_Linear - Accuracy: 0.917, AUC: 0.955
2025-05-28 21:24:15,989 - INFO - Training SVM_RBF...
2025-05-28 21:24:16,031 - INFO - SVM_RBF - Accuracy: 0.917, AUC: 1.000
2025-05-28 21:24:16,031 - INFO - Training SVM_Poly...
2025-05-28 21:24:16,072 - INFO - SVM_Poly - Accuracy: 0.958, AUC: 0.955
2025-05-28 21:24:16,072 - INFO - Training Decision_Tree...
2025-05-28 21:24:16,100 - INFO - Decision_Tree - Accuracy: 0.917, AUC: 0.955
2025-05-28 21:24:16,112 - INFO - Training Gradient_Boosting...
2025-05-28 21:24:16,679 - INFO - Gradient_Boosting - Accuracy: 0.917, AUC: 0.943
2025-05-28 21:24:16,679 - INFO - Training KNN_3...
2025-05-28 21:24:16,732 - INFO - KNN_3 - Accuracy: 1.000, AUC: 1.000
2025-05-28 21:24:16,733 - INFO - Training KNN_5...
2025-05-28 21:24:16,772 - INFO - KNN_5 - Accuracy: 1.000, AUC: 1.000
2025-05-28 21:24:16,772 - INFO - Training KNN_7...
2025-05-28 21:24:16,818 - INFO - KNN_7 - Accuracy: 0.958, AUC: 0.989
2025-05-28 21:24:16,818 - INFO - Training Naive_Bayes...
2025-05-28 21:24:16,852 - INFO - Naive_Bayes - Accuracy: 0.958, AUC: 1.000
2025-05-28 21:24:16,852 - INFO - Model training and evaluation completed
2025-05-28 21:24:16,852 - INFO - Generating ROC curves...
2025-05-28 21:24:17,561 - INFO - ROC curves saved
2025-05-28 21:24:17,561 - INFO - Generating confusion matrices...
2025-05-28 21:24:24,753 - INFO - Confusion matrices saved
2025-05-28 21:24:24,753 - INFO - Comparing results with R analysis...
2025-05-28 21:24:24,789 - INFO - R analysis comparison completed
2025-05-28 21:24:24,796 - INFO - Generating summary report...
2025-05-28 21:24:24,811 - ERROR - Analysis pipeline failed: 'f1-score'
2025-05-28 21:25:22,188 - INFO - Created output directories
2025-05-28 21:25:22,188 - INFO - Starting complete analysis pipeline...
2025-05-28 21:25:22,571 - INFO - Successfully loaded data from SAMBIT THESIS DATA version3.xlsx
2025-05-28 21:25:22,571 - INFO - Dataset shape: (120, 28)
2025-05-28 21:25:22,571 - INFO - Columns: ['CASE NO', 'NAME', 'AGE', 'SEX', 'Difficult Laryngoscopy ', 'HEIGHT', 'HEIGHT IN METER', 'WEIGHT', 'BMI', 'ASA GRADE', 'MPG SCORE', 'INTER INCISOR DISTANCE', 'SHBD', 'STHMD', 'SED', 'TH', 'OH', 'TTOHR', 'TTFUSG', "HAN'S SCORE", 'HANS_Category', 'CL GRADE', 'CL GRADE.1', 'Difficult Laryngoscopy .1', 'INTUBATION', 'NO OF ATTEMPTS', 'CHANGE OF HAND', 'Difficult Laryngoscopy']
2025-05-28 21:25:22,571 - INFO - Data validation successful
2025-05-28 21:25:22,571 - INFO - Starting exploratory data analysis...
2025-05-28 21:25:25,432 - INFO - EDA plots saved to output/figures/
2025-05-28 21:25:25,432 - INFO - Exploratory data analysis completed
2025-05-28 21:25:25,433 - INFO - Starting data preprocessing...
2025-05-28 21:25:25,435 - INFO - Applied min-max scaling to match R normalization
2025-05-28 21:25:25,461 - INFO - Preprocessed data saved
2025-05-28 21:25:25,461 - INFO - Data preprocessing completed
2025-05-28 21:25:25,461 - INFO - Initializing classification models...
2025-05-28 21:25:25,461 - INFO - Initialized 15 models
2025-05-28 21:25:25,461 - INFO - Starting model training and evaluation...
2025-05-28 21:25:25,461 - INFO - Training Logistic_Regression_L1...
2025-05-28 21:25:25,506 - INFO - Logistic_Regression_L1 - Accuracy: 0.917, AUC: 0.943
2025-05-28 21:25:25,506 - INFO - Training Logistic_Regression_L2...
2025-05-28 21:25:25,570 - INFO - Logistic_Regression_L2 - Accuracy: 0.917, AUC: 0.955
2025-05-28 21:25:25,571 - INFO - Training Logistic_Regression_Elastic...
2025-05-28 21:25:25,607 - INFO - Logistic_Regression_Elastic - Accuracy: 0.917, AUC: 0.955
2025-05-28 21:25:25,608 - INFO - Training Random_Forest_50...
2025-05-28 21:25:25,979 - INFO - Random_Forest_50 - Accuracy: 0.917, AUC: 0.955
2025-05-28 21:25:25,979 - INFO - Training Random_Forest_100...
2025-05-28 21:25:26,667 - INFO - Random_Forest_100 - Accuracy: 0.958, AUC: 0.955
2025-05-28 21:25:26,667 - INFO - Training Random_Forest_200...
2025-05-28 21:25:28,032 - INFO - Random_Forest_200 - Accuracy: 0.875, AUC: 0.955
2025-05-28 21:25:28,032 - INFO - Training SVM_Linear...
2025-05-28 21:25:28,066 - INFO - SVM_Linear - Accuracy: 0.917, AUC: 0.955
2025-05-28 21:25:28,066 - INFO - Training SVM_RBF...
2025-05-28 21:25:28,106 - INFO - SVM_RBF - Accuracy: 0.917, AUC: 1.000
2025-05-28 21:25:28,106 - INFO - Training SVM_Poly...
2025-05-28 21:25:28,152 - INFO - SVM_Poly - Accuracy: 0.958, AUC: 0.955
2025-05-28 21:25:28,152 - INFO - Training Decision_Tree...
2025-05-28 21:25:28,184 - INFO - Decision_Tree - Accuracy: 0.917, AUC: 0.955
2025-05-28 21:25:28,185 - INFO - Training Gradient_Boosting...
2025-05-28 21:25:28,665 - INFO - Gradient_Boosting - Accuracy: 0.917, AUC: 0.943
2025-05-28 21:25:28,665 - INFO - Training KNN_3...
2025-05-28 21:25:28,715 - INFO - KNN_3 - Accuracy: 1.000, AUC: 1.000
2025-05-28 21:25:28,715 - INFO - Training KNN_5...
2025-05-28 21:25:28,776 - INFO - KNN_5 - Accuracy: 1.000, AUC: 1.000
2025-05-28 21:25:28,776 - INFO - Training KNN_7...
2025-05-28 21:25:28,821 - INFO - KNN_7 - Accuracy: 0.958, AUC: 0.989
2025-05-28 21:25:28,821 - INFO - Training Naive_Bayes...
2025-05-28 21:25:28,865 - INFO - Naive_Bayes - Accuracy: 0.958, AUC: 1.000
2025-05-28 21:25:28,870 - INFO - Model training and evaluation completed
2025-05-28 21:25:28,870 - INFO - Generating ROC curves...
2025-05-28 21:25:29,546 - INFO - ROC curves saved
2025-05-28 21:25:29,546 - INFO - Generating confusion matrices...
2025-05-28 21:25:36,371 - INFO - Confusion matrices saved
2025-05-28 21:25:36,371 - INFO - Comparing results with R analysis...
2025-05-28 21:25:36,407 - INFO - R analysis comparison completed
2025-05-28 21:25:36,407 - INFO - Generating summary report...
2025-05-28 21:25:36,434 - INFO - Summary report generated
2025-05-28 21:25:36,435 - INFO - Complete analysis pipeline finished successfully!
2025-05-28 21:25:36,446 - INFO - Sample prediction function created
