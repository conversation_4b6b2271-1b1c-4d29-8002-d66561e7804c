#!/usr/bin/env python3
"""
Advanced Machine Learning Models for Difficult Laryngoscopy Prediction

This script builds upon basic_models.py with advanced techniques including:
- Ensemble methods (Voting, Stacking, Bagging)
- Neural networks (MLPClassifier)
- Hyperparameter tuning (GridSearchCV, RandomizedSearchCV)
- Feature selection and engineering
- Advanced evaluation techniques

Author: AI Assistant
Date: 2024
"""

import os
import sys
import warnings
import logging
import argparse
import pickle
from datetime import datetime
from typing import Dict, List, Tuple, Any, Optional

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.model_selection import (
    GridSearchCV, RandomizedSearchCV, StratifiedKFold,
    learning_curve, validation_curve, cross_val_score
)
from sklearn.ensemble import (
    VotingClassifier, StackingClassifier, BaggingClassifier,
    AdaBoostClassifier, ExtraTreesClassifier
)
from sklearn.neural_network import MLPClassifier
from sklearn.feature_selection import (
    SelectKBest, f_classif, RFE, SelectFromModel
)
from sklearn.preprocessing import PolynomialFeatures
from sklearn.calibration import calibration_curve, CalibratedClassifierCV
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    confusion_matrix, classification_report, roc_curve, auc,
    roc_auc_score, brier_score_loss
)
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.svm import SVC
from sklearn.tree import DecisionTreeClassifier
from sklearn.neighbors import KNeighborsClassifier
from sklearn.naive_bayes import GaussianNB
import joblib

# Try importing advanced libraries
try:
    import xgboost as xgb
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False
    warnings.warn("XGBoost not available. Install with: pip install xgboost")

try:
    import lightgbm as lgb
    LIGHTGBM_AVAILABLE = True
except ImportError:
    LIGHTGBM_AVAILABLE = False
    warnings.warn("LightGBM not available. Install with: pip install lightgbm")

# Configure warnings and logging
warnings.filterwarnings('ignore')
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('advanced_models.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Set random seed for reproducibility
RANDOM_STATE = 42
np.random.seed(RANDOM_STATE)

class AdvancedLaryngoscopyPredictor:
    """
    Advanced machine learning pipeline for predicting difficult laryngoscopy.

    This class extends the basic models with advanced techniques including
    ensemble methods, neural networks, hyperparameter tuning, and feature engineering.
    """

    def __init__(self, basic_models_path: str = "output/results/preprocessed_data.pkl"):
        """
        Initialize the advanced predictor.

        Args:
            basic_models_path: Path to preprocessed data from basic_models.py
        """
        self.basic_models_path = basic_models_path
        self.X_train = None
        self.X_test = None
        self.y_train = None
        self.y_test = None
        self.feature_names = None
        self.scaler = None
        self.models = {}
        self.tuned_models = {}
        self.ensemble_models = {}
        self.results = {}
        self.feature_importance = {}

        # Create output directories
        self._create_output_dirs()

    def _create_output_dirs(self) -> None:
        """Create necessary output directories."""
        dirs = [
            'output/advanced_figures',
            'output/advanced_models',
            'output/advanced_results',
            'output/feature_analysis'
        ]
        for dir_path in dirs:
            os.makedirs(dir_path, exist_ok=True)
        logger.info("Created advanced output directories")

    def load_preprocessed_data(self) -> None:
        """
        Load preprocessed data from basic_models.py output.
        """
        try:
            # Load preprocessed datasets
            with open(self.basic_models_path, 'rb') as f:
                data = pickle.load(f)

            self.X_train = data['X_train']
            self.X_test = data['X_test']
            self.y_train = data['y_train']
            self.y_test = data['y_test']
            self.feature_names = data['feature_names']

            # Load scaler
            self.scaler = joblib.load('output/models/scaler.pkl')

            logger.info(f"Loaded preprocessed data: {self.X_train.shape[0]} train, {self.X_test.shape[0]} test samples")

        except FileNotFoundError:
            logger.error(f"Preprocessed data not found at {self.basic_models_path}")
            logger.error("Please run basic_models.py first to generate preprocessed data")
            raise
        except Exception as e:
            logger.error(f"Error loading preprocessed data: {str(e)}")
            raise

    def load_basic_models(self) -> Dict[str, Any]:
        """
        Load trained models from basic_models.py for ensemble creation.

        Returns:
            Dictionary of loaded models
        """
        basic_models = {}
        models_dir = 'output/models'

        if not os.path.exists(models_dir):
            logger.warning("Basic models directory not found. Skipping model loading.")
            return basic_models

        # Find the most recent models
        model_files = [f for f in os.listdir(models_dir) if f.endswith('.pkl') and f != 'scaler.pkl']

        for model_file in model_files:
            try:
                model_path = os.path.join(models_dir, model_file)
                model = joblib.load(model_path)
                model_name = model_file.replace('.pkl', '').split('_')[0:2]  # Get base name
                model_name = '_'.join(model_name)
                basic_models[model_name] = model
                logger.info(f"Loaded basic model: {model_name}")
            except Exception as e:
                logger.warning(f"Could not load model {model_file}: {str(e)}")

        return basic_models

    def feature_selection_analysis(self) -> Dict[str, Any]:
        """
        Perform comprehensive feature selection analysis.

        Returns:
            Dictionary containing feature selection results
        """
        logger.info("Starting feature selection analysis...")

        feature_selection_results = {}

        # 1. Univariate feature selection (SelectKBest)
        selector_univariate = SelectKBest(score_func=f_classif, k='all')
        selector_univariate.fit(self.X_train, self.y_train)

        univariate_scores = pd.DataFrame({
            'feature': self.feature_names,
            'score': selector_univariate.scores_,
            'p_value': selector_univariate.pvalues_
        }).sort_values('score', ascending=False)

        feature_selection_results['univariate'] = univariate_scores

        # 2. Recursive Feature Elimination (RFE)
        base_estimator = LogisticRegression(random_state=RANDOM_STATE)
        rfe_selector = RFE(estimator=base_estimator, n_features_to_select=3)
        rfe_selector.fit(self.X_train, self.y_train)

        rfe_results = pd.DataFrame({
            'feature': self.feature_names,
            'selected': rfe_selector.support_,
            'ranking': rfe_selector.ranking_
        }).sort_values('ranking')

        feature_selection_results['rfe'] = rfe_results

        # 3. Feature importance from tree-based models
        rf_model = RandomForestClassifier(n_estimators=100, random_state=RANDOM_STATE)
        rf_model.fit(self.X_train, self.y_train)

        importance_results = pd.DataFrame({
            'feature': self.feature_names,
            'importance': rf_model.feature_importances_
        }).sort_values('importance', ascending=False)

        feature_selection_results['tree_importance'] = importance_results
        self.feature_importance['random_forest'] = importance_results

        # Save results
        for method, results in feature_selection_results.items():
            results.to_csv(f'output/feature_analysis/{method}_feature_selection.csv', index=False)

        # Visualize feature importance
        self._plot_feature_importance(feature_selection_results)

        logger.info("Feature selection analysis completed")
        return feature_selection_results

    def _plot_feature_importance(self, feature_results: Dict[str, pd.DataFrame]) -> None:
        """Plot feature importance from different methods."""
        fig, axes = plt.subplots(1, 3, figsize=(18, 6))

        # Univariate scores
        univariate_data = feature_results['univariate']
        axes[0].barh(univariate_data['feature'], univariate_data['score'])
        axes[0].set_title('Univariate Feature Scores (F-statistic)')
        axes[0].set_xlabel('F-statistic Score')

        # RFE ranking (lower is better)
        rfe_data = feature_results['rfe']
        colors = ['green' if selected else 'red' for selected in rfe_data['selected']]
        axes[1].barh(rfe_data['feature'], 1/rfe_data['ranking'], color=colors)
        axes[1].set_title('RFE Feature Ranking (Higher = Better)')
        axes[1].set_xlabel('1/Ranking Score')

        # Tree importance
        importance_data = feature_results['tree_importance']
        axes[2].barh(importance_data['feature'], importance_data['importance'])
        axes[2].set_title('Random Forest Feature Importance')
        axes[2].set_xlabel('Importance Score')

        plt.tight_layout()
        plt.savefig('output/feature_analysis/feature_importance_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()

    def create_ensemble_models(self) -> Dict[str, Any]:
        """
        Create various ensemble models.

        Returns:
            Dictionary of ensemble models
        """
        logger.info("Creating ensemble models...")

        # Base models for ensembles
        base_models = [
            ('lr', LogisticRegression(random_state=RANDOM_STATE)),
            ('rf', RandomForestClassifier(n_estimators=100, random_state=RANDOM_STATE)),
            ('svm', SVC(probability=True, random_state=RANDOM_STATE)),
            ('gb', GradientBoostingClassifier(random_state=RANDOM_STATE))
        ]

        ensemble_models = {}

        # 1. Voting Classifier (Hard voting)
        voting_hard = VotingClassifier(
            estimators=base_models,
            voting='hard'
        )
        ensemble_models['Voting_Hard'] = voting_hard

        # 2. Voting Classifier (Soft voting)
        voting_soft = VotingClassifier(
            estimators=base_models,
            voting='soft'
        )
        ensemble_models['Voting_Soft'] = voting_soft

        # 3. Stacking Classifier
        stacking = StackingClassifier(
            estimators=base_models,
            final_estimator=LogisticRegression(random_state=RANDOM_STATE),
            cv=5
        )
        ensemble_models['Stacking'] = stacking

        # 4. Bagging with Decision Trees
        bagging = BaggingClassifier(
            base_estimator=DecisionTreeClassifier(random_state=RANDOM_STATE),
            n_estimators=100,
            random_state=RANDOM_STATE
        )
        ensemble_models['Bagging'] = bagging

        # 5. AdaBoost
        ada_boost = AdaBoostClassifier(
            base_estimator=DecisionTreeClassifier(max_depth=1, random_state=RANDOM_STATE),
            n_estimators=100,
            random_state=RANDOM_STATE
        )
        ensemble_models['AdaBoost'] = ada_boost

        # 6. Extra Trees
        extra_trees = ExtraTreesClassifier(
            n_estimators=100,
            random_state=RANDOM_STATE
        )
        ensemble_models['ExtraTrees'] = extra_trees

        # 7. Advanced boosting models (if available)
        if XGBOOST_AVAILABLE:
            xgb_model = xgb.XGBClassifier(
                random_state=RANDOM_STATE,
                eval_metric='logloss'
            )
            ensemble_models['XGBoost'] = xgb_model

        if LIGHTGBM_AVAILABLE:
            lgb_model = lgb.LGBMClassifier(
                random_state=RANDOM_STATE,
                verbose=-1
            )
            ensemble_models['LightGBM'] = lgb_model

        self.ensemble_models = ensemble_models
        logger.info(f"Created {len(ensemble_models)} ensemble models")
        return ensemble_models

    def create_neural_networks(self) -> Dict[str, MLPClassifier]:
        """
        Create neural network models with different architectures.

        Returns:
            Dictionary of neural network models
        """
        logger.info("Creating neural network models...")

        neural_networks = {}

        # 1. Single hidden layer
        nn_single = MLPClassifier(
            hidden_layer_sizes=(10,),
            activation='relu',
            solver='adam',
            max_iter=1000,
            random_state=RANDOM_STATE,
            early_stopping=True,
            validation_fraction=0.2
        )
        neural_networks['NN_Single_Layer'] = nn_single

        # 2. Two hidden layers
        nn_double = MLPClassifier(
            hidden_layer_sizes=(20, 10),
            activation='relu',
            solver='adam',
            max_iter=1000,
            random_state=RANDOM_STATE,
            early_stopping=True,
            validation_fraction=0.2
        )
        neural_networks['NN_Double_Layer'] = nn_double

        # 3. Three hidden layers
        nn_triple = MLPClassifier(
            hidden_layer_sizes=(30, 20, 10),
            activation='relu',
            solver='adam',
            max_iter=1000,
            random_state=RANDOM_STATE,
            early_stopping=True,
            validation_fraction=0.2
        )
        neural_networks['NN_Triple_Layer'] = nn_triple

        # 4. Different activation functions
        nn_tanh = MLPClassifier(
            hidden_layer_sizes=(20, 10),
            activation='tanh',
            solver='adam',
            max_iter=1000,
            random_state=RANDOM_STATE,
            early_stopping=True,
            validation_fraction=0.2
        )
        neural_networks['NN_Tanh'] = nn_tanh

        # 5. Different solver
        nn_lbfgs = MLPClassifier(
            hidden_layer_sizes=(10,),
            activation='relu',
            solver='lbfgs',
            max_iter=1000,
            random_state=RANDOM_STATE
        )
        neural_networks['NN_LBFGS'] = nn_lbfgs

        logger.info(f"Created {len(neural_networks)} neural network models")
        return neural_networks

    def hyperparameter_tuning(self) -> Dict[str, Any]:
        """
        Perform hyperparameter tuning for key models.

        Returns:
            Dictionary of tuned models
        """
        logger.info("Starting hyperparameter tuning...")

        tuned_models = {}

        # 1. Random Forest tuning
        rf_param_grid = {
            'n_estimators': [50, 100, 200],
            'max_depth': [5, 10, None],
            'min_samples_split': [2, 5, 10],
            'min_samples_leaf': [1, 2, 4]
        }

        rf_grid = GridSearchCV(
            RandomForestClassifier(random_state=RANDOM_STATE),
            rf_param_grid,
            cv=5,
            scoring='roc_auc',
            n_jobs=-1
        )
        rf_grid.fit(self.X_train, self.y_train)
        tuned_models['RF_Tuned'] = rf_grid.best_estimator_

        # 2. SVM tuning
        svm_param_grid = {
            'C': [0.1, 1, 10, 100],
            'gamma': ['scale', 'auto', 0.001, 0.01, 0.1, 1],
            'kernel': ['rbf', 'poly']
        }

        svm_grid = RandomizedSearchCV(
            SVC(probability=True, random_state=RANDOM_STATE),
            svm_param_grid,
            n_iter=20,
            cv=5,
            scoring='roc_auc',
            n_jobs=-1,
            random_state=RANDOM_STATE
        )
        svm_grid.fit(self.X_train, self.y_train)
        tuned_models['SVM_Tuned'] = svm_grid.best_estimator_

        # 3. Gradient Boosting tuning
        gb_param_grid = {
            'n_estimators': [50, 100, 200],
            'learning_rate': [0.01, 0.1, 0.2],
            'max_depth': [3, 5, 7]
        }

        gb_grid = GridSearchCV(
            GradientBoostingClassifier(random_state=RANDOM_STATE),
            gb_param_grid,
            cv=5,
            scoring='roc_auc',
            n_jobs=-1
        )
        gb_grid.fit(self.X_train, self.y_train)
        tuned_models['GB_Tuned'] = gb_grid.best_estimator_

        # 4. Neural Network tuning
        nn_param_grid = {
            'hidden_layer_sizes': [(10,), (20,), (10, 5), (20, 10)],
            'activation': ['relu', 'tanh'],
            'alpha': [0.0001, 0.001, 0.01]
        }

        nn_grid = RandomizedSearchCV(
            MLPClassifier(max_iter=1000, random_state=RANDOM_STATE),
            nn_param_grid,
            n_iter=10,
            cv=5,
            scoring='roc_auc',
            n_jobs=-1,
            random_state=RANDOM_STATE
        )
        nn_grid.fit(self.X_train, self.y_train)
        tuned_models['NN_Tuned'] = nn_grid.best_estimator_

        self.tuned_models = tuned_models

        # Save tuning results
        tuning_results = {
            'RF_best_params': rf_grid.best_params_,
            'RF_best_score': rf_grid.best_score_,
            'SVM_best_params': svm_grid.best_params_,
            'SVM_best_score': svm_grid.best_score_,
            'GB_best_params': gb_grid.best_params_,
            'GB_best_score': gb_grid.best_score_,
            'NN_best_params': nn_grid.best_params_,
            'NN_best_score': nn_grid.best_score_
        }

        with open('output/advanced_results/hyperparameter_tuning_results.pkl', 'wb') as f:
            pickle.dump(tuning_results, f)

        logger.info("Hyperparameter tuning completed")
        return tuned_models

    def advanced_evaluation(self, models: Dict[str, Any]) -> Dict[str, Dict[str, float]]:
        """
        Perform advanced evaluation including learning curves and calibration.

        Args:
            models: Dictionary of models to evaluate

        Returns:
            Dictionary containing evaluation results
        """
        logger.info("Starting advanced evaluation...")

        results = {}

        for model_name, model in models.items():
            logger.info(f"Evaluating {model_name}...")

            try:
                # Train the model
                model.fit(self.X_train, self.y_train)

                # Basic predictions
                y_pred = model.predict(self.X_test)
                y_pred_proba = model.predict_proba(self.X_test)[:, 1] if hasattr(model, 'predict_proba') else None

                # Calculate comprehensive metrics
                metrics = {
                    'accuracy': accuracy_score(self.y_test, y_pred),
                    'precision': precision_score(self.y_test, y_pred, zero_division=0),
                    'recall': recall_score(self.y_test, y_pred, zero_division=0),
                    'f1_score': f1_score(self.y_test, y_pred, zero_division=0),
                    'specificity': self._calculate_specificity(self.y_test, y_pred)
                }

                # Add AUC and calibration metrics if probability predictions available
                if y_pred_proba is not None:
                    metrics['auc'] = roc_auc_score(self.y_test, y_pred_proba)
                    metrics['brier_score'] = brier_score_loss(self.y_test, y_pred_proba)
                else:
                    metrics['auc'] = np.nan
                    metrics['brier_score'] = np.nan

                # Cross-validation with multiple metrics
                cv_scores = {}
                for scoring in ['accuracy', 'precision', 'recall', 'f1', 'roc_auc']:
                    try:
                        scores = cross_val_score(
                            model, self.X_train, self.y_train,
                            cv=StratifiedKFold(n_splits=5, shuffle=True, random_state=RANDOM_STATE),
                            scoring=scoring
                        )
                        cv_scores[f'cv_{scoring}_mean'] = scores.mean()
                        cv_scores[f'cv_{scoring}_std'] = scores.std()
                    except Exception:
                        cv_scores[f'cv_{scoring}_mean'] = np.nan
                        cv_scores[f'cv_{scoring}_std'] = np.nan

                metrics.update(cv_scores)
                results[model_name] = metrics

                # Save trained model
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                model_path = f'output/advanced_models/{model_name}_{timestamp}.pkl'
                joblib.dump(model, model_path)

                logger.info(f"{model_name} - Accuracy: {metrics['accuracy']:.3f}, AUC: {metrics['auc']:.3f}")

            except Exception as e:
                logger.error(f"Error evaluating {model_name}: {str(e)}")
                results[model_name] = {'error': str(e)}

        # Save results
        results_df = pd.DataFrame(results).T
        results_df.to_csv('output/advanced_results/advanced_evaluation_results.csv')

        logger.info("Advanced evaluation completed")
        return results

    def _calculate_specificity(self, y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """Calculate specificity (True Negative Rate)."""
        tn, fp, _, _ = confusion_matrix(y_true, y_pred).ravel()
        return tn / (tn + fp) if (tn + fp) > 0 else 0.0

    def generate_learning_curves(self, models: Dict[str, Any]) -> None:
        """Generate learning curves for model diagnosis."""
        logger.info("Generating learning curves...")

        n_models = len(models)
        n_cols = 3
        n_rows = (n_models + n_cols - 1) // n_cols

        _, axes = plt.subplots(n_rows, n_cols, figsize=(15, 5 * n_rows))
        axes = axes.flatten() if n_rows > 1 else [axes] if n_cols == 1 else axes

        for idx, (model_name, model) in enumerate(models.items()):
            try:
                train_sizes, train_scores, val_scores = learning_curve(
                    model, self.X_train, self.y_train,
                    cv=5, n_jobs=-1, train_sizes=np.linspace(0.1, 1.0, 10),
                    scoring='roc_auc'
                )

                train_mean = np.mean(train_scores, axis=1)
                train_std = np.std(train_scores, axis=1)
                val_mean = np.mean(val_scores, axis=1)
                val_std = np.std(val_scores, axis=1)

                axes[idx].plot(train_sizes, train_mean, 'o-', color='blue', label='Training Score')
                axes[idx].fill_between(train_sizes, train_mean - train_std, train_mean + train_std, alpha=0.1, color='blue')

                axes[idx].plot(train_sizes, val_mean, 'o-', color='red', label='Validation Score')
                axes[idx].fill_between(train_sizes, val_mean - val_std, val_mean + val_std, alpha=0.1, color='red')

                axes[idx].set_title(f'{model_name} Learning Curve')
                axes[idx].set_xlabel('Training Set Size')
                axes[idx].set_ylabel('AUC Score')
                axes[idx].legend()
                axes[idx].grid(True, alpha=0.3)

            except Exception as e:
                axes[idx].text(0.5, 0.5, f'Error: {str(e)}', ha='center', va='center', transform=axes[idx].transAxes)
                axes[idx].set_title(f'{model_name} (Error)')

        # Hide unused subplots
        for idx in range(len(models), len(axes)):
            axes[idx].set_visible(False)

        plt.tight_layout()
        plt.savefig('output/advanced_figures/learning_curves.png', dpi=300, bbox_inches='tight')
        plt.close()

        logger.info("Learning curves saved")

    def generate_calibration_plots(self, models: Dict[str, Any]) -> None:
        """Generate calibration plots for probability assessment."""
        logger.info("Generating calibration plots...")

        plt.figure(figsize=(12, 8))

        for model_name, model in models.items():
            if hasattr(model, 'predict_proba'):
                try:
                    model.fit(self.X_train, self.y_train)
                    y_pred_proba = model.predict_proba(self.X_test)[:, 1]

                    fraction_of_positives, mean_predicted_value = calibration_curve(
                        self.y_test, y_pred_proba, n_bins=10
                    )

                    plt.plot(mean_predicted_value, fraction_of_positives, "s-", label=model_name)

                except Exception as e:
                    logger.warning(f"Could not generate calibration plot for {model_name}: {str(e)}")

        plt.plot([0, 1], [0, 1], "k:", label="Perfectly calibrated")
        plt.xlabel("Mean Predicted Probability")
        plt.ylabel("Fraction of Positives")
        plt.title("Calibration Plots")
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig('output/advanced_figures/calibration_plots.png', dpi=300, bbox_inches='tight')
        plt.close()

        logger.info("Calibration plots saved")

    def create_feature_engineered_models(self) -> Dict[str, Any]:
        """
        Create models with engineered features (polynomial features, interactions).

        Returns:
            Dictionary of models with engineered features
        """
        logger.info("Creating feature engineered models...")

        # Create polynomial features
        poly = PolynomialFeatures(degree=2, include_bias=False, interaction_only=True)
        X_train_poly = poly.fit_transform(self.X_train)
        X_test_poly = poly.transform(self.X_test)

        # Create models with polynomial features
        engineered_models = {}

        # Logistic Regression with polynomial features
        lr_poly = LogisticRegression(random_state=RANDOM_STATE, max_iter=1000)
        lr_poly.fit(X_train_poly, self.y_train)
        engineered_models['LR_Polynomial'] = (lr_poly, X_test_poly)

        # Random Forest with polynomial features
        rf_poly = RandomForestClassifier(n_estimators=100, random_state=RANDOM_STATE)
        rf_poly.fit(X_train_poly, self.y_train)
        engineered_models['RF_Polynomial'] = (rf_poly, X_test_poly)

        logger.info(f"Created {len(engineered_models)} feature engineered models")
        return engineered_models

    def run_complete_advanced_analysis(self) -> None:
        """
        Run the complete advanced machine learning analysis pipeline.
        """
        logger.info("Starting complete advanced analysis pipeline...")

        try:
            # Load preprocessed data
            self.load_preprocessed_data()

            # Feature selection analysis
            feature_results = self.feature_selection_analysis()

            # Create ensemble models
            ensemble_models = self.create_ensemble_models()

            # Create neural networks
            neural_networks = self.create_neural_networks()

            # Hyperparameter tuning
            tuned_models = self.hyperparameter_tuning()

            # Combine all models for evaluation
            all_models = {**ensemble_models, **neural_networks, **tuned_models}

            # Advanced evaluation
            self.results = self.advanced_evaluation(all_models)

            # Generate advanced visualizations
            self.generate_learning_curves(tuned_models)  # Use tuned models for learning curves
            self.generate_calibration_plots(all_models)

            # Feature engineering
            engineered_models = self.create_feature_engineered_models()

            # Generate final summary
            self._generate_advanced_summary()

            logger.info("Complete advanced analysis pipeline finished successfully!")
            print("\n" + "="*60)
            print("ADVANCED ANALYSIS COMPLETED SUCCESSFULLY!")
            print("="*60)
            print("Check the 'output' directory for:")
            print("- Advanced Figures: output/advanced_figures/")
            print("- Advanced Models: output/advanced_models/")
            print("- Advanced Results: output/advanced_results/")
            print("- Feature Analysis: output/feature_analysis/")
            print("- Logs: advanced_models.log")

        except Exception as e:
            logger.error(f"Advanced analysis pipeline failed: {str(e)}")
            raise

    def _generate_advanced_summary(self) -> None:
        """Generate comprehensive advanced analysis summary."""
        logger.info("Generating advanced summary...")

        results_df = pd.DataFrame(self.results).T

        if not results_df.empty:
            # Best models by different metrics
            best_models = {}
            for metric in ['accuracy', 'auc', 'f1_score', 'precision', 'recall']:
                if metric in results_df.columns:
                    best_models[f'Best {metric.upper()}'] = results_df[metric].idxmax()

            print("\n" + "="*60)
            print("ADVANCED MODEL PERFORMANCE SUMMARY")
            print("="*60)
            print("Top 5 models by AUC:")
            if 'auc' in results_df.columns:
                top_auc = results_df.nlargest(5, 'auc')[['accuracy', 'auc', 'f1_score', 'precision', 'recall']]
                print(top_auc)

            print(f"\nBest models by metric:")
            for metric, model_name in best_models.items():
                if model_name in results_df.index:
                    score = results_df.loc[model_name, metric.lower().replace('best ', '')]
                    print(f"{metric}: {model_name} ({score:.3f})")

            # Save advanced summary
            with open('output/advanced_results/advanced_summary_report.txt', 'w') as f:
                f.write("DIFFICULT LARYNGOSCOPY PREDICTION - ADVANCED MODEL SUMMARY\n")
                f.write("="*60 + "\n\n")
                f.write(f"Advanced models evaluated: {len(self.results)}\n")
                f.write(f"Features analyzed: {', '.join(self.feature_names)}\n\n")
                f.write("Best models by metric:\n")
                for metric, model_name in best_models.items():
                    if model_name in results_df.index:
                        score = results_df.loc[model_name, metric.lower().replace('best ', '')]
                        f.write(f"{metric}: {model_name} ({score:.3f})\n")

        logger.info("Advanced summary generated")


def main():
    """
    Main function with command-line interface for advanced analysis.
    """
    parser = argparse.ArgumentParser(
        description='Advanced Machine Learning Models for Difficult Laryngoscopy Prediction'
    )
    parser.add_argument(
        '--basic-data',
        type=str,
        default='output/results/preprocessed_data.pkl',
        help='Path to preprocessed data from basic_models.py'
    )
    parser.add_argument(
        '--quick',
        action='store_true',
        help='Run quick analysis with fewer models'
    )
    parser.add_argument(
        '--skip-tuning',
        action='store_true',
        help='Skip hyperparameter tuning (faster execution)'
    )

    args = parser.parse_args()

    # Check if basic analysis has been run
    if not os.path.exists(args.basic_data):
        print("Error: Preprocessed data not found!")
        print("Please run basic_models.py first to generate preprocessed data.")
        sys.exit(1)

    # Initialize advanced predictor
    predictor = AdvancedLaryngoscopyPredictor(basic_models_path=args.basic_data)

    if args.quick:
        # Quick analysis
        logger.info("Running quick advanced analysis...")
        predictor.load_preprocessed_data()

        # Create only key ensemble models
        ensemble_models = {
            'Voting_Soft': VotingClassifier(
                estimators=[
                    ('lr', LogisticRegression(random_state=RANDOM_STATE)),
                    ('rf', RandomForestClassifier(random_state=RANDOM_STATE))
                ],
                voting='soft'
            ),
            'Stacking': StackingClassifier(
                estimators=[
                    ('lr', LogisticRegression(random_state=RANDOM_STATE)),
                    ('rf', RandomForestClassifier(random_state=RANDOM_STATE))
                ],
                final_estimator=LogisticRegression(random_state=RANDOM_STATE)
            )
        }

        # Quick neural network
        neural_networks = {
            'NN_Simple': MLPClassifier(
                hidden_layer_sizes=(10,),
                random_state=RANDOM_STATE,
                max_iter=500
            )
        }

        all_models = {**ensemble_models, **neural_networks}
        predictor.results = predictor.advanced_evaluation(all_models)
        predictor._generate_advanced_summary()

    else:
        # Full advanced analysis
        predictor.run_complete_advanced_analysis()

    print(f"\nAdvanced analysis completed! Check the logs for details.")


if __name__ == "__main__":
    main()
