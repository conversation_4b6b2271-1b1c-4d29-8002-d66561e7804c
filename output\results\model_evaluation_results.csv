,accuracy,precision,recall,f1_score,specificity,auc,cv_mean,cv_std
Logistic_Regression_L1,0.9166666666666666,0.0,0.0,0.0,1.0,0.9431818181818181,0.9378947368421052,0.0189473684210526
Logistic_Regression_L2,0.9166666666666666,0.0,0.0,0.0,1.0,0.9545454545454545,0.9378947368421052,0.0189473684210526
Logistic_Regression_Elastic,0.9166666666666666,0.0,0.0,0.0,1.0,0.9545454545454545,0.9378947368421052,0.0189473684210526
Random_Forest_50,0.9166666666666666,0.5,0.5,0.5,0.9545454545454546,0.9545454545454546,0.9168421052631579,0.04162888407007407
Random_Forest_100,0.9583333333333334,0.6666666666666666,1.0,0.8,0.9545454545454546,0.9545454545454546,0.9168421052631579,0.04162888407007407
Random_Forest_200,0.875,0.0,0.0,0.0,0.9545454545454546,0.9545454545454546,0.9168421052631579,0.04162888407007407
SVM_Linear,0.9166666666666666,0.0,0.0,0.0,1.0,0.9545454545454545,0.9378947368421052,0.0189473684210526
SVM_RBF,0.9166666666666666,0.0,0.0,0.0,1.0,1.0,0.9378947368421052,0.0189473684210526
SVM_Poly,0.9583333333333334,0.6666666666666666,1.0,0.8,0.9545454545454546,0.9545454545454546,0.9473684210526315,0.0332871332649303
Decision_Tree,0.9166666666666666,0.5,1.0,0.6666666666666666,0.9090909090909091,0.9545454545454545,0.9068421052631578,0.036902206616753884
Gradient_Boosting,0.9166666666666666,0.5,0.5,0.5,0.9545454545454546,0.9431818181818181,0.9068421052631578,0.036902206616753884
KNN_3,1.0,1.0,1.0,1.0,1.0,1.0,0.9373684210526315,0.039540294675774613
KNN_5,1.0,1.0,1.0,1.0,1.0,1.0,0.9168421052631578,0.024998614920079796
KNN_7,0.9583333333333334,1.0,0.5,0.6666666666666666,1.0,0.9886363636363636,0.9273684210526316,0.024551376399348603
Naive_Bayes,0.9583333333333334,0.6666666666666666,1.0,0.8,0.9545454545454546,1.0,0.8952631578947369,0.0476715447335758
