Sam = read_excel("SAMBIT THESIS DATA version3.xlsx")

Sam
normalize = function(X) {return((X-min(X))/(max(X)-min(X)))}
Sam$nSHBD = normalize(Sam$SHBD)
View(nSHBD)
Sam$nSTHMD = normalize(Sam$STHMD)
Sam$nSED = normalize(Sam$SED)
Sam$nTTOHR = normalize(Sam$TTOHR)

View(Sam)
library(pROC)
names(Sam)

nttohr_roc = roc(Sam$`Difficult Laryngoscopy...24`,Sam$nTTOHR)
plot(nttohr_roc)
auc(nttohr_roc)

coords(ttohr_roc,"best")
coords(ttohr_roc,"best",ret = c("se","sp","ppv",
                                "npv","accuracy"))
RGLM1 = glm(`Difficult Laryngoscopy...24`~nTTOHR+nSTHMD+nSHBD+nSED,
            family='binomial',data = Sam)
summary(RGLM1)
RGLM2 = glm(`Difficult Laryngoscopy...24`~nTTOHR+nSTHMD+nSED,
            family='binomial',data = Sam)
summary(RGLM2)
RGLM3 = glm(`Difficult Laryngoscopy...24`~nTTOHR+nSTHMD+nSHBD,
            family='binomial',data = Sam)
summary(RGLM3)
RGLM4 = glm(`Difficult Laryngoscopy...24`~nTTOHR+nSHBD+nSED,
            family='binomial',data = Sam)
summary(RGLM4)
RGLM5 = glm(`Difficult Laryngoscopy...24`~nSHBD+nSTHMD+nSED,
            family='binomial',data = Sam)
summary(RGLM5)
