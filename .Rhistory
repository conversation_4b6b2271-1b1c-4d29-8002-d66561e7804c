library(readxl)
sam <- read_excel("SAMBIT THESIS DATA version2).xlsx")
library(pROC)
View(sam)
names(sam)
ttohr_roc = roc(sam$`Difficult Laryngoscopy`,sam$TTOHR)
plot(ttohr_roc)
auc(ttohr_roc)
coords(ttohr_roc)
coords(ttohr_roc,"best")
coords(ttohr_roc,"best",ret = c("se","sp","ppv","npv","accuracy"))
ci.auc(ttohr_roc)
ci.coords(ttohr_roc,"best",ret = c("se","sp","ppv",
"npv","accuracy"))
coords(ttohr_roc,"best",ret = c("se","sp","ppv",
"npv","accuracy"))
coords(ttohr_roc,"best",ret = c("se","sp","ppv",
"npv","accuracy"))
coords(ttohr_roc,"best")
ci.coords(ttohr_roc,x=0.79,ret = c("se","sp","ppv",
"npv","accuracy"))
ci.coords(ttohr_roc,x=0.79,ret = c("se"))
ci.coords(ttohr_roc,x=0.79,ret = c("sp"))
ci.coords(ttohr_roc,x=0.79,ret = c("accuracy"))
names(ttohr_roc)
names(sam)
fit1=glm(`Difficult Laryngoscopy`~TTOHR+SHBD,
family='binomial')
fit1=glm(`Difficult Laryngoscopy`~TTOHR+SHBD,
family='binomial',data = sam)
fit1=glm(`Difficult Laryngoscopy`~TTOHR+SHBD,
family='binomial',data = sam)
fit2=glm(`Difficult Laryngoscopy`~TTOHR, family='binomial')
fit2=glm(`Difficult Laryngoscopy`~TTOHR,
family='binomial',data = sam)
preds=predict(fit1)
roc1=roc(`Difficult Laryngoscopy` ~ preds)
roc1=roc(sam$`Difficult Laryngoscopy` ~ preds)
preds2=predict(fit2)
roc2=roc(sam$`Difficult Laryngoscopy` ~ preds2)
plot(roc1)
plot(roc2, add=TRUE, col='red')
roc.test(roc1,roc2)
names(sam)
fit3=glm(`Difficult Laryngoscopy`~TTOHR+SHBD+SED,
family='binomial',data = sam)
preds3=predict(fit3)
roc3=roc(sam$`Difficult Laryngoscopy` ~ preds3)
pre
plot(roc1)
plot(roc3, add=TRUE, col='green')
roc.test(roc3,roc2)
summary(fit3)
summary(fit3)
fit3
