# Difficult Laryngoscopy Prediction - Machine Learning Analysis

This project implements comprehensive machine learning models to predict "Difficult Laryngoscopy" using medical dataset features. The analysis extends existing R-based research with advanced Python ML techniques.

## Project Overview

The project consists of two main Python scripts that implement and evaluate multiple classification models:

1. **`basic_models.py`** - Fundamental ML models and evaluation
2. **`advanced_models.py`** - Advanced techniques including ensemble methods, neural networks, and hyperparameter tuning

## Dataset

- **Source**: `SAMBIT THESIS DATA version3.xlsx` (121 patients)
- **Target Variable**: "Difficult Laryngoscopy" (0=EASY, 1=DIFFICULT)
- **Key Features**: TTOHR, SHBD, STHMD, SED
- **Preprocessing**: Min-max normalization to match R analysis methodology
- **Note**: HEIGHT column excluded as specified in requirements

## Features

### Basic Models (`basic_models.py`)
- **Data Loading & Validation**: Robust Excel/CSV loading with error handling
- **Exploratory Data Analysis**: Comprehensive visualizations and statistics
- **Multiple Classifiers**:
  - Logistic Regression (L1, L2, Elastic Net)
  - Random Forest (various configurations)
  - Support Vector Machine (Linear, RBF, Polynomial)
  - Decision Tree, Gradient Boosting
  - K-Nearest Neighbors, Naive Bayes
- **Evaluation Metrics**: Accuracy, Precision, Recall, F1-score, Specificity, AUC
- **Visualizations**: ROC curves, confusion matrices, feature distributions
- **R Analysis Comparison**: Direct comparison with existing GLM models

### Advanced Models (`advanced_models.py`)
- **Ensemble Methods**: Voting, Stacking, Bagging, AdaBoost, Extra Trees
- **Neural Networks**: MLPClassifier with various architectures
- **Advanced Boosting**: XGBoost, LightGBM (if available)
- **Hyperparameter Tuning**: GridSearchCV and RandomizedSearchCV
- **Feature Engineering**: Polynomial features and interactions
- **Advanced Evaluation**: Learning curves, calibration plots, cross-validation
- **Feature Selection**: Univariate, RFE, tree-based importance

## Installation

1. **Clone or download the project files**
2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

### Required Dependencies
- scikit-learn==1.3.1
- pandas==2.1.3
- numpy==1.24.3
- matplotlib==3.7.2
- seaborn==0.12.2
- openpyxl==3.1.2
- joblib==1.3.2
- scipy==1.11.4
- statsmodels==0.14.0

### Optional Dependencies (for advanced features)
- xgboost==2.0.1
- lightgbm==4.1.0

## Usage

### Basic Analysis

```bash
# Full analysis
python basic_models.py

# Quick analysis (fewer models)
python basic_models.py --quick

# Custom data path
python basic_models.py --data "path/to/your/data.xlsx"
```

### Advanced Analysis

```bash
# Full advanced analysis (run basic_models.py first)
python advanced_models.py

# Quick advanced analysis
python advanced_models.py --quick

# Skip hyperparameter tuning (faster)
python advanced_models.py --skip-tuning
```

## Output Structure

```
output/
├── figures/                    # Basic analysis visualizations
│   ├── feature_distributions.png
│   ├── correlation_heatmap.png
│   ├── roc_curves_comparison.png
│   └── confusion_matrices.png
├── models/                     # Trained models
│   ├── scaler.pkl
│   └── [model_name]_[timestamp].pkl
├── results/                    # Basic analysis results
│   ├── model_evaluation_results.csv
│   ├── r_analysis_comparison.csv
│   └── summary_report.txt
├── advanced_figures/           # Advanced visualizations
│   ├── learning_curves.png
│   └── calibration_plots.png
├── advanced_models/            # Advanced trained models
├── advanced_results/           # Advanced analysis results
└── feature_analysis/           # Feature selection results
```

## Key Features

### Data Preprocessing
- **Min-max normalization**: `(X-min(X))/(max(X)-min(X))` to match R methodology
- **Missing value handling**: Median imputation for numerical features
- **Stratified train-test split**: 80/20 split maintaining class distribution
- **Reproducibility**: Fixed random seeds for consistent results

### Model Evaluation
- **Comprehensive metrics**: Accuracy, Precision, Recall, F1-score, Specificity, AUC
- **Cross-validation**: 5-fold stratified cross-validation
- **Statistical comparison**: Model performance comparison with confidence intervals
- **R analysis validation**: Direct comparison with existing GLM results

### Advanced Techniques
- **Ensemble learning**: Multiple voting and stacking strategies
- **Neural networks**: Various architectures with early stopping
- **Hyperparameter optimization**: Grid and randomized search
- **Feature engineering**: Polynomial and interaction features
- **Model diagnosis**: Learning curves and calibration analysis

## Sample Prediction

After running the analysis, use the generated prediction function:

```python
from output.sample_prediction import predict_difficult_laryngoscopy

# Example prediction
prediction, probability = predict_difficult_laryngoscopy(
    ttohr=0.8, shbd=1.5, sthmd=1.8, sed=2.0
)

print(f"Prediction: {'Difficult' if prediction else 'Easy'}")
print(f"Probability: {probability:.3f}")
```

## Comparison with R Analysis

The Python implementation replicates and extends the R analysis:

- **GLM equivalence**: Logistic regression models match R GLM results
- **Feature normalization**: Identical min-max scaling methodology
- **Statistical validation**: Comparable performance metrics
- **Extended analysis**: Additional ML algorithms and evaluation techniques

## Logging and Debugging

- **Comprehensive logging**: All operations logged to `basic_models.log` and `advanced_models.log`
- **Error handling**: Robust exception handling with informative error messages
- **Progress tracking**: Real-time progress updates during model training
- **Reproducibility**: All random operations use fixed seeds

## Performance Optimization

- **Parallel processing**: Multi-core utilization for cross-validation and grid search
- **Memory efficiency**: Efficient data handling for large datasets
- **Quick modes**: Reduced model sets for faster execution
- **Caching**: Model and preprocessing pipeline serialization

## Contributing

1. Follow PEP 8 style guidelines
2. Add comprehensive docstrings
3. Include type hints
4. Write unit tests for new features
5. Update documentation

## License

This project is part of medical research. Please ensure appropriate ethical approval and data privacy compliance when using with real patient data.

## Citation

If you use this code in your research, please cite:
```
Difficult Laryngoscopy Prediction using Machine Learning
Python Implementation extending R-based GLM Analysis
[Your Institution/Research Group]
```
