#!/usr/bin/env python3
"""
Installation and Basic Functionality Test Script

This script tests the installation of required dependencies and basic functionality
of the difficult laryngoscopy prediction models.

Run this script before executing the main analysis to ensure everything is set up correctly.
"""

import sys
import os
import warnings
from typing import List, <PERSON><PERSON>

def test_imports() -> Tuple[bool, List[str]]:
    """
    Test if all required packages can be imported.
    
    Returns:
        Tuple of (success, missing_packages)
    """
    print("Testing package imports...")
    
    required_packages = [
        ('pandas', 'pandas'),
        ('numpy', 'numpy'),
        ('sklearn', 'scikit-learn'),
        ('matplotlib.pyplot', 'matplotlib'),
        ('seaborn', 'seaborn'),
        ('scipy', 'scipy'),
        ('joblib', 'joblib'),
        ('openpyxl', 'openpyxl')
    ]
    
    optional_packages = [
        ('xgboost', 'xgboost'),
        ('lightgbm', 'lightgbm')
    ]
    
    missing_required = []
    missing_optional = []
    
    # Test required packages
    for import_name, package_name in required_packages:
        try:
            __import__(import_name)
            print(f"✓ {package_name}")
        except ImportError:
            print(f"✗ {package_name} - MISSING")
            missing_required.append(package_name)
    
    # Test optional packages
    print("\nOptional packages:")
    for import_name, package_name in optional_packages:
        try:
            __import__(import_name)
            print(f"✓ {package_name}")
        except ImportError:
            print(f"⚠ {package_name} - Optional (install for advanced features)")
            missing_optional.append(package_name)
    
    if missing_required:
        print(f"\n❌ Missing required packages: {', '.join(missing_required)}")
        print("Install with: pip install " + " ".join(missing_required))
        return False, missing_required
    else:
        print("\n✅ All required packages are available!")
        if missing_optional:
            print(f"💡 Optional packages missing: {', '.join(missing_optional)}")
        return True, []

def test_data_availability() -> bool:
    """
    Test if the dataset is available.
    
    Returns:
        True if data is found, False otherwise
    """
    print("\nTesting data availability...")
    
    data_files = [
        'SAMBIT THESIS DATA version3.xlsx',
        'ai.csv'
    ]
    
    found_data = False
    for data_file in data_files:
        if os.path.exists(data_file):
            print(f"✓ Found data file: {data_file}")
            found_data = True
            break
    
    if not found_data:
        print("⚠ No data files found. Expected files:")
        for data_file in data_files:
            print(f"  - {data_file}")
        print("The scripts will attempt to load data, but may fail if files are not available.")
    
    return found_data

def test_basic_functionality() -> bool:
    """
    Test basic functionality of the prediction classes.
    
    Returns:
        True if basic functionality works, False otherwise
    """
    print("\nTesting basic functionality...")
    
    try:
        # Test basic imports from our modules
        import pandas as pd
        import numpy as np
        from sklearn.linear_model import LogisticRegression
        from sklearn.model_selection import train_test_split
        from sklearn.preprocessing import MinMaxScaler
        
        # Create synthetic test data
        np.random.seed(42)
        n_samples = 100
        n_features = 4
        
        X = np.random.randn(n_samples, n_features)
        y = (X[:, 0] + X[:, 1] > 0).astype(int)  # Simple synthetic target
        
        # Test preprocessing
        scaler = MinMaxScaler()
        X_scaled = scaler.fit_transform(X)
        
        # Test train-test split
        X_train, X_test, y_train, y_test = train_test_split(
            X_scaled, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # Test model training
        model = LogisticRegression(random_state=42)
        model.fit(X_train, y_train)
        
        # Test prediction
        y_pred = model.predict(X_test)
        y_pred_proba = model.predict_proba(X_test)
        
        # Test basic metrics
        from sklearn.metrics import accuracy_score, roc_auc_score
        accuracy = accuracy_score(y_test, y_pred)
        auc = roc_auc_score(y_test, y_pred_proba[:, 1])
        
        print(f"✓ Synthetic data test - Accuracy: {accuracy:.3f}, AUC: {auc:.3f}")
        
        # Test directory creation
        os.makedirs('output/test', exist_ok=True)
        print("✓ Directory creation works")
        
        # Test file operations
        test_data = pd.DataFrame({'test': [1, 2, 3]})
        test_data.to_csv('output/test/test_file.csv', index=False)
        loaded_data = pd.read_csv('output/test/test_file.csv')
        assert len(loaded_data) == 3
        print("✓ File I/O operations work")
        
        # Cleanup
        os.remove('output/test/test_file.csv')
        os.rmdir('output/test')
        
        print("✅ Basic functionality test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Basic functionality test failed: {str(e)}")
        return False

def test_script_syntax() -> bool:
    """
    Test if the main scripts have valid syntax.
    
    Returns:
        True if syntax is valid, False otherwise
    """
    print("\nTesting script syntax...")
    
    scripts = ['basic_models.py', 'advanced_models.py']
    
    for script in scripts:
        if os.path.exists(script):
            try:
                with open(script, 'r') as f:
                    compile(f.read(), script, 'exec')
                print(f"✓ {script} - syntax OK")
            except SyntaxError as e:
                print(f"❌ {script} - syntax error: {e}")
                return False
        else:
            print(f"⚠ {script} - file not found")
    
    print("✅ Script syntax test passed!")
    return True

def main():
    """
    Run all tests and provide summary.
    """
    print("=" * 60)
    print("DIFFICULT LARYNGOSCOPY PREDICTION - INSTALLATION TEST")
    print("=" * 60)
    
    # Suppress warnings for cleaner output
    warnings.filterwarnings('ignore')
    
    # Run tests
    imports_ok, missing_packages = test_imports()
    data_ok = test_data_availability()
    functionality_ok = test_basic_functionality()
    syntax_ok = test_script_syntax()
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    if imports_ok and functionality_ok and syntax_ok:
        print("🎉 ALL TESTS PASSED!")
        print("\nYou can now run the analysis scripts:")
        print("  python basic_models.py")
        print("  python advanced_models.py")
        
        if not data_ok:
            print("\n⚠ Note: No data files detected. Make sure your dataset is available.")
        
        return True
    else:
        print("❌ SOME TESTS FAILED!")
        
        if not imports_ok:
            print(f"\n📦 Install missing packages: pip install {' '.join(missing_packages)}")
        
        if not functionality_ok:
            print("\n🔧 Check your Python environment and package versions")
        
        if not syntax_ok:
            print("\n📝 Check script files for syntax errors")
        
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
