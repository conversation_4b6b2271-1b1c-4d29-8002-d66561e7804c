#!/usr/bin/env python3
"""
Complete Analysis Runner

This script runs the complete difficult laryngoscopy prediction analysis
by executing both basic_models.py and advanced_models.py in sequence.

Usage:
    python run_analysis.py [--quick] [--data path/to/data.xlsx]
"""

import os
import sys
import argparse
import subprocess
import time
from datetime import datetime

def run_command(command, description):
    """
    Run a command and handle errors.
    
    Args:
        command: Command to run as a list
        description: Description of what the command does
    
    Returns:
        True if successful, False otherwise
    """
    print(f"\n{'='*60}")
    print(f"RUNNING: {description}")
    print(f"{'='*60}")
    print(f"Command: {' '.join(command)}")
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    start_time = time.time()
    
    try:
        result = subprocess.run(command, check=True, capture_output=False)
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n✅ {description} completed successfully!")
        print(f"Duration: {duration:.1f} seconds")
        return True
        
    except subprocess.CalledProcessError as e:
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n❌ {description} failed!")
        print(f"Error code: {e.returncode}")
        print(f"Duration: {duration:.1f} seconds")
        return False
    except FileNotFoundError:
        print(f"\n❌ {description} failed!")
        print("Error: Python script not found")
        return False

def check_prerequisites():
    """
    Check if required files exist.
    
    Returns:
        True if prerequisites are met, False otherwise
    """
    print("Checking prerequisites...")
    
    required_files = ['basic_models.py', 'advanced_models.py', 'requirements.txt']
    missing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✓ {file}")
        else:
            print(f"❌ {file} - MISSING")
            missing_files.append(file)
    
    if missing_files:
        print(f"\nMissing required files: {', '.join(missing_files)}")
        return False
    
    print("✅ All required files found!")
    return True

def main():
    """
    Main function to run the complete analysis.
    """
    parser = argparse.ArgumentParser(
        description='Run complete difficult laryngoscopy prediction analysis'
    )
    parser.add_argument(
        '--quick', 
        action='store_true',
        help='Run quick analysis with fewer models'
    )
    parser.add_argument(
        '--data', 
        type=str, 
        default='SAMBIT THESIS DATA version3.xlsx',
        help='Path to the dataset file'
    )
    parser.add_argument(
        '--skip-advanced', 
        action='store_true',
        help='Skip advanced analysis (run only basic models)'
    )
    parser.add_argument(
        '--test-first', 
        action='store_true',
        help='Run installation test before analysis'
    )
    
    args = parser.parse_args()
    
    print("🔬 DIFFICULT LARYNGOSCOPY PREDICTION - COMPLETE ANALYSIS")
    print("=" * 60)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Quick mode: {'Yes' if args.quick else 'No'}")
    print(f"Data file: {args.data}")
    print(f"Skip advanced: {'Yes' if args.skip_advanced else 'No'}")
    
    overall_start_time = time.time()
    
    # Check prerequisites
    if not check_prerequisites():
        print("\n❌ Prerequisites not met. Exiting.")
        return False
    
    # Run installation test if requested
    if args.test_first:
        test_command = [sys.executable, 'test_installation.py']
        if not run_command(test_command, "Installation Test"):
            print("\n❌ Installation test failed. Please fix issues before running analysis.")
            return False
    
    # Prepare commands
    basic_command = [sys.executable, 'basic_models.py', '--data', args.data]
    if args.quick:
        basic_command.append('--quick')
    
    advanced_command = [sys.executable, 'advanced_models.py']
    if args.quick:
        advanced_command.append('--quick')
    
    # Run basic analysis
    if not run_command(basic_command, "Basic Models Analysis"):
        print("\n❌ Basic analysis failed. Cannot proceed to advanced analysis.")
        return False
    
    # Run advanced analysis (if not skipped)
    if not args.skip_advanced:
        if not run_command(advanced_command, "Advanced Models Analysis"):
            print("\n❌ Advanced analysis failed.")
            return False
    
    # Calculate total time
    overall_end_time = time.time()
    total_duration = overall_end_time - overall_start_time
    
    # Final summary
    print(f"\n{'='*60}")
    print("🎉 COMPLETE ANALYSIS FINISHED!")
    print(f"{'='*60}")
    print(f"Total duration: {total_duration:.1f} seconds ({total_duration/60:.1f} minutes)")
    print(f"Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    print(f"\n📁 Results are available in the 'output' directory:")
    print("   📊 Figures: output/figures/ and output/advanced_figures/")
    print("   🤖 Models: output/models/ and output/advanced_models/")
    print("   📈 Results: output/results/ and output/advanced_results/")
    print("   🔍 Feature Analysis: output/feature_analysis/")
    print("   📝 Logs: basic_models.log and advanced_models.log")
    
    print(f"\n💡 Next steps:")
    print("   1. Review the summary reports in output/results/")
    print("   2. Examine the visualizations in output/figures/")
    print("   3. Use the best models for predictions with output/sample_prediction.py")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ Analysis interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ Unexpected error: {str(e)}")
        sys.exit(1)
